#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阅读量抓取调试工具
用于诊断阅读量获取失败的原因
"""

import re
import requests
import json
import os
from datetime import datetime
from batch_readnum_spider import BatchReadnumSpider


def debug_single_article(article_url):
    """调试单篇文章的阅读量抓取"""
    print(f"🔍 调试文章: {article_url}")
    print("="*80)
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败")
        return
    
    try:
        # 发送请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate, br",
            "Cookie": spider.cookie_str,
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        print("📡 发送请求...")
        response = requests.get(article_url, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📏 响应长度: {len(response.text)} 字符")
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return
        
        html_content = response.text
        
        # 保存原始HTML用于调试
        debug_dir = "./debug"
        os.makedirs(debug_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_file = os.path.join(debug_dir, f"article_debug_{timestamp}.html")
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 原始HTML已保存到: {html_file}")
        
        # 调试各种数据提取
        print("\n🔍 调试数据提取:")
        print("-" * 50)
        
        # 1. 检查基本信息
        title_match = re.search(r'<meta property="og:title" content="(.*?)"', html_content)
        author_match = re.search(r'<meta property="og:article:author" content="(.*?)"', html_content)
        
        title = title_match.group(1) if title_match else "未找到标题"
        author = author_match.group(1) if author_match else "未找到作者"
        
        print(f"📰 标题: {title}")
        print(f"👤 作者: {author}")
        
        # 2. 调试阅读量提取
        print(f"\n📖 调试阅读量提取:")
        read_patterns = [
            r"var cgiData = {[^}]*?read_num: '(\d+)'",
            r"read_num:\s*'(\d+)'",
            r"read_num:\s*(\d+)",
            r'"read_num":"(\d+)"',
            r'"read_num":(\d+)',
            r'read_num=(\d+)',
            r'readNum["\']?\s*:\s*["\']?(\d+)',
            r'阅读\s*(\d+)',
        ]
        
        read_count = 0
        for i, pattern in enumerate(read_patterns):
            match = re.search(pattern, html_content)
            if match:
                read_count = int(match.group(1))
                print(f"✅ 模式 {i+1} 匹配成功: {read_count}")
                break
            else:
                print(f"❌ 模式 {i+1} 未匹配: {pattern}")
        
        # 3. 调试点赞数提取
        print(f"\n👍 调试点赞数提取:")
        like_patterns = [
            r"window\.appmsg_bar_data = {[^}]*?like_count: '(\d+)'",
            r"like_count:\s*'(\d+)'",
            r"like_count:\s*(\d+)",
            r'"like_count":"(\d+)"',
            r'"like_count":(\d+)',
            r'like_count=(\d+)',
            r'likeNum["\']?\s*:\s*["\']?(\d+)',
            r'点赞\s*(\d+)',
        ]
        
        like_count = 0
        for i, pattern in enumerate(like_patterns):
            match = re.search(pattern, html_content)
            if match:
                like_count = int(match.group(1))
                print(f"✅ 模式 {i+1} 匹配成功: {like_count}")
                break
            else:
                print(f"❌ 模式 {i+1} 未匹配: {pattern}")
        
        # 4. 调试分享数提取
        print(f"\n📤 调试分享数提取:")
        share_patterns = [
            r"window\.appmsg_bar_data = {[^}]*?share_count: '(\d+)'",
            r"share_count:\s*'(\d+)'",
            r"share_count:\s*(\d+)",
            r'"share_count":"(\d+)"',
            r'"share_count":(\d+)',
            r'share_count=(\d+)',
            r'shareNum["\']?\s*:\s*["\']?(\d+)',
            r'分享\s*(\d+)',
        ]
        
        share_count = 0
        for i, pattern in enumerate(share_patterns):
            match = re.search(pattern, html_content)
            if match:
                share_count = int(match.group(1))
                print(f"✅ 模式 {i+1} 匹配成功: {share_count}")
                break
            else:
                print(f"❌ 模式 {i+1} 未匹配: {pattern}")
        
        # 5. 搜索可能的数据结构
        print(f"\n🔍 搜索可能的数据结构:")
        
        # 搜索包含数字的JavaScript变量
        js_vars = re.findall(r'(var\s+\w+\s*=\s*{[^}]*\d+[^}]*})', html_content)
        print(f"📊 找到 {len(js_vars)} 个包含数字的JS变量")
        
        for i, var in enumerate(js_vars[:3]):  # 只显示前3个
            print(f"   {i+1}. {var[:100]}...")
        
        # 搜索window对象上的数据
        window_data = re.findall(r'(window\.\w+\s*=\s*{[^}]*\d+[^}]*})', html_content)
        print(f"🪟 找到 {len(window_data)} 个window对象数据")
        
        for i, data in enumerate(window_data[:3]):  # 只显示前3个
            print(f"   {i+1}. {data[:100]}...")
        
        # 6. 最终结果
        print(f"\n📊 最终提取结果:")
        print(f"   📖 阅读量: {read_count}")
        print(f"   👍 点赞数: {like_count}")
        print(f"   📤 分享数: {share_count}")
        
        # 7. 保存调试结果
        debug_result = {
            "url": article_url,
            "title": title,
            "author": author,
            "read_count": read_count,
            "like_count": like_count,
            "share_count": share_count,
            "response_status": response.status_code,
            "response_length": len(html_content),
            "debug_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "html_file": html_file
        }
        
        debug_json = os.path.join(debug_dir, f"debug_result_{timestamp}.json")
        with open(debug_json, 'w', encoding='utf-8') as f:
            json.dump(debug_result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 调试结果已保存到: {debug_json}")
        
        return debug_result
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def search_patterns_in_html(html_file):
    """在HTML文件中搜索可能的数据模式"""
    print(f"🔍 在HTML文件中搜索数据模式: {html_file}")
    
    if not os.path.exists(html_file):
        print(f"❌ 文件不存在: {html_file}")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📏 文件大小: {len(content)} 字符")
    
    # 搜索所有包含数字的模式
    patterns_to_search = [
        (r'read_num["\']?\s*:\s*["\']?(\d+)', "阅读量模式"),
        (r'like_count["\']?\s*:\s*["\']?(\d+)', "点赞数模式"),
        (r'share_count["\']?\s*:\s*["\']?(\d+)', "分享数模式"),
        (r'comment_count["\']?\s*:\s*["\']?(\d+)', "评论数模式"),
        (r'(\w*read\w*)["\']?\s*:\s*["\']?(\d+)', "包含read的字段"),
        (r'(\w*like\w*)["\']?\s*:\s*["\']?(\d+)', "包含like的字段"),
        (r'(\w*share\w*)["\']?\s*:\s*["\']?(\d+)', "包含share的字段"),
    ]
    
    for pattern, description in patterns_to_search:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"✅ {description}: 找到 {len(matches)} 个匹配")
            for match in matches[:5]:  # 只显示前5个
                if isinstance(match, tuple):
                    print(f"   {match}")
                else:
                    print(f"   {match}")
        else:
            print(f"❌ {description}: 未找到匹配")


def main():
    """主函数"""
    print("🔧 微信公众号阅读量抓取调试工具")
    print("="*60)
    
    # 选择调试模式
    print("请选择调试模式:")
    print("1. 调试单篇文章URL")
    print("2. 分析已保存的HTML文件")
    print("3. 使用测试URL")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        url = input("请输入文章URL: ").strip()
        if url:
            debug_single_article(url)
        else:
            print("❌ URL不能为空")
    
    elif choice == "2":
        html_file = input("请输入HTML文件路径: ").strip()
        if html_file:
            search_patterns_in_html(html_file)
        else:
            print("❌ 文件路径不能为空")
    
    elif choice == "3":
        # 使用一个测试URL
        test_url = "http://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247484156&idx=1&sn=example"
        print(f"使用测试URL: {test_url}")
        debug_single_article(test_url)
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
