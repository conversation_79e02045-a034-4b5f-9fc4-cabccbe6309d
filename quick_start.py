#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号批量阅读量抓取器 - 快速启动脚本
适合有经验的用户快速开始抓取
"""

import os
from batch_readnum_spider import BatchReadnumSpider

def quick_batch_crawl():
    """快速批量抓取（使用默认参数）"""
    print("🚀 快速启动批量阅读量抓取")
    print("=" * 50)
    
    # 检查cookie文件
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到cookie文件 (wechat_keys.txt)")
        print("💡 请先运行以下命令抓取Cookie:")
        print("   python main_enhanced.py")
        print("   然后选择功能1进行Cookie抓取")
        return
    
    # 使用默认参数：3页，每页10篇，最近7天
    print("📋 使用默认配置:")
    print("   - 最大页数: 3")
    print("   - 每页文章数: 10")
    print("   - 时间范围: 最近7天")
    print("   - 预计抓取: 最多30篇文章")
    
    confirm = input("\n是否继续？(y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 已取消")
        return
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    try:
        print(f"\n🚀 开始批量抓取...")
        
        # 执行抓取
        results = spider.batch_crawl_readnum(
            max_pages=3,
            articles_per_page=10,
            days_back=7
        )
        
        if results:
            # 显示统计摘要
            spider.print_summary()
            
            # 保存数据
            print(f"\n💾 正在保存数据...")
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            
            print(f"\n🎉 抓取完成！")
            if excel_file:
                print(f"📊 Excel文件: {excel_file}")
            if json_file:
                print(f"💾 JSON文件: {json_file}")
                
            # 打开文件夹
            try:
                import subprocess
                subprocess.run(['explorer', os.path.dirname(excel_file)], check=False)
                print("📁 已自动打开数据文件夹")
            except:
                pass
        else:
            print("❌ 未获取到任何数据")
            print("💡 可能的解决方案:")
            print("   1. 检查Cookie是否有效: python main_enhanced.py (选择功能4)")
            print("   2. 重新抓取Cookie: python main_enhanced.py (选择功能1)")
            print("   3. 检查网络连接")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
        if spider.articles_data:
            print("💾 保存已抓取的数据...")
            spider.save_to_excel()
            spider.save_to_json()
    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        print("💡 建议:")
        print("   1. 运行测试脚本: python test_batch_readnum.py")
        print("   2. 使用完整版程序: python main_enhanced.py")

def main():
    """主函数"""
    print("⚡ 微信公众号批量阅读量抓取器 - 快速启动")
    print("适合有经验的用户，使用默认参数快速开始抓取")
    
    quick_batch_crawl()

if __name__ == "__main__":
    main()
