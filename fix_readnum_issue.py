#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复阅读量获取问题的解决方案
"""

import os
import json
from datetime import datetime
from batch_readnum_spider import BatchReadnumSpider


def test_with_real_article():
    """使用真实文章URL测试阅读量抓取"""
    print("🔧 阅读量抓取问题修复方案")
    print("="*60)
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败")
        return
    
    print("\n📋 问题分析:")
    print("1. 测试URL返回'参数错误'，说明URL无效")
    print("2. 需要使用真实的文章URL进行测试")
    print("3. Cookie可能需要更新")
    
    print("\n🔍 建议的解决步骤:")
    print("1. 首先获取真实的文章列表")
    print("2. 使用文章列表中的URL进行阅读量抓取")
    print("3. 如果仍然失败，更新Cookie")
    
    # 尝试获取文章列表
    print("\n📖 尝试获取文章列表...")
    articles = spider.get_article_list(begin_page=0, count=5)
    
    if not articles:
        print("❌ 无法获取文章列表，可能Cookie已过期")
        print("\n💡 解决方案:")
        print("1. 运行 python main_enhanced.py")
        print("2. 选择功能1重新抓取Cookie")
        print("3. 然后重新尝试批量阅读量抓取")
        return
    
    print(f"✅ 成功获取 {len(articles)} 篇文章")
    
    # 测试第一篇文章的阅读量抓取
    if articles:
        test_article = articles[0]
        print(f"\n🧪 测试文章: {test_article['title'][:30]}...")
        print(f"📄 URL: {test_article['url'][:80]}...")
        
        # 抓取统计数据
        stats = spider.extract_article_stats(test_article['url'])
        
        if stats:
            print(f"\n✅ 成功获取统计数据:")
            print(f"   📖 阅读量: {stats['read_count']}")
            print(f"   👍 点赞数: {stats['like_count']}")
            print(f"   📤 分享数: {stats['share_count']}")
            
            if stats['read_count'] > 0 or stats['like_count'] > 0 or stats['share_count'] > 0:
                print("\n🎉 阅读量抓取功能正常！")
                print("💡 之前的问题是因为使用了无效的测试URL")
            else:
                print("\n⚠️ 统计数据为0，可能的原因:")
                print("1. 文章刚发布，数据还未更新")
                print("2. 微信页面结构发生变化")
                print("3. 需要特殊的请求参数")
        else:
            print("\n❌ 统计数据获取失败")
            print("💡 建议:")
            print("1. 检查Cookie是否有效")
            print("2. 尝试手动访问文章URL确认可访问性")
            print("3. 运行调试工具进一步分析")


def create_usage_guide():
    """创建使用指南"""
    guide = """
# 🔧 阅读量获取问题解决指南

## 📊 问题现象
- 阅读量、点赞数、分享数都显示为0
- 提示"参数错误"

## 🔍 问题原因
1. **URL无效**: 使用了测试URL或过期URL
2. **Cookie过期**: 认证信息失效
3. **页面结构变化**: 微信更新了页面结构

## ✅ 解决方案

### 方案1: 使用真实文章URL
```bash
# 1. 先获取文章列表
python main_enhanced.py
# 选择功能2，获取真实的文章链接

# 2. 使用真实URL进行批量阅读量抓取
python main_enhanced.py
# 选择功能3，批量阅读量统计抓取
```

### 方案2: 更新Cookie
```bash
# 1. 重新抓取Cookie
python main_enhanced.py
# 选择功能1，抓取Cookie

# 2. 确认Cookie状态
python main_enhanced.py
# 选择功能4，查看Cookie状态
```

### 方案3: 调试分析
```bash
# 使用调试工具分析具体问题
python debug_readnum.py
# 选择模式1，输入真实文章URL
```

## 📋 正确的使用流程

1. **首次使用**:
   ```bash
   python main_enhanced.py
   # 1. 抓取Cookie
   # 2. 基础文章链接抓取（获取真实URL）
   # 3. 批量阅读量统计抓取
   ```

2. **日常使用**:
   ```bash
   python main_enhanced.py
   # 直接选择功能3进行批量阅读量抓取
   ```

3. **遇到问题时**:
   ```bash
   python diagnose.py  # 系统诊断
   python debug_readnum.py  # 阅读量调试
   ```

## 💡 注意事项

1. **不要使用测试URL**: 确保使用真实的微信文章链接
2. **定期更新Cookie**: 建议每天重新抓取一次Cookie
3. **控制抓取频率**: 避免过于频繁的请求导致IP被封
4. **检查网络环境**: 确保能正常访问微信公众号文章

## 🎯 预期结果

正常情况下，阅读量抓取应该显示：
```
📊 抓取统计数据: http://mp.weixin.qq.com/s?__biz=...
🔍 阅读量匹配模式 1: 1234
🔍 点赞数匹配模式 2: 56
🔍 分享数匹配模式 1: 78
✅ 统计数据: 阅读1234 点赞56 分享78
```

如果仍然显示全部为0，请运行调试工具进一步分析。
"""
    
    with open("阅读量问题解决指南.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📖 使用指南已保存到: 阅读量问题解决指南.md")


def main():
    """主函数"""
    print("🔧 阅读量抓取问题修复工具")
    print("="*50)
    
    print("选择操作:")
    print("1. 测试真实文章URL")
    print("2. 创建使用指南")
    print("3. 全部执行")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        test_with_real_article()
    elif choice == "2":
        create_usage_guide()
    elif choice == "3":
        test_with_real_article()
        create_usage_guide()
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
