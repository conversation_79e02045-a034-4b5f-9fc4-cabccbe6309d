#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量阅读量抓取器测试脚本
用于验证功能是否正常工作
"""

import os
import sys
from batch_readnum_spider import BatchReadnumSpider
from read_cookie import ReadCookie

def test_cookie_loading():
    """测试Cookie加载功能"""
    print("🧪 测试1: Cookie加载功能")
    print("-" * 40)
    
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到wechat_keys.txt文件")
        print("💡 请先运行main_enhanced.py选择功能1抓取Cookie")
        return False
    
    try:
        cookie_reader = ReadCookie('wechat_keys.txt')
        result = cookie_reader.get_latest_cookies()
        
        if result:
            print("✅ Cookie加载成功")
            print(f"   __biz: {result['biz']}")
            print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
            print(f"   cookie长度: {len(result['cookie_str'])} 字符")
            return True
        else:
            print("❌ Cookie解析失败")
            return False
    except Exception as e:
        print(f"❌ Cookie加载出错: {e}")
        return False

def test_spider_initialization():
    """测试爬虫初始化"""
    print("\n🧪 测试2: 爬虫初始化")
    print("-" * 40)
    
    try:
        spider = BatchReadnumSpider()
        
        if spider.appmsg_token and spider.biz and spider.cookie_str:
            print("✅ 爬虫初始化成功")
            print(f"   认证信息完整")
            print(f"   数据目录: ./data/readnum_batch")
            return spider
        else:
            print("❌ 爬虫初始化失败，认证信息不完整")
            return None
    except Exception as e:
        print(f"❌ 爬虫初始化出错: {e}")
        return None

def test_article_list_fetching(spider):
    """测试文章列表获取"""
    print("\n🧪 测试3: 文章列表获取")
    print("-" * 40)
    
    try:
        # 只获取第一页的5篇文章进行测试
        articles = spider.get_article_list(begin_page=0, count=5)
        
        if articles:
            print(f"✅ 成功获取 {len(articles)} 篇文章")
            for i, article in enumerate(articles[:3]):  # 只显示前3篇
                print(f"   {i+1}. {article['title'][:30]}...")
            return articles
        else:
            print("❌ 未获取到文章列表")
            print("💡 可能原因:")
            print("   1. Cookie已过期")
            print("   2. 网络连接问题")
            print("   3. 微信API限制")
            return []
    except Exception as e:
        print(f"❌ 文章列表获取出错: {e}")
        return []

def test_single_article_stats(spider, articles):
    """测试单篇文章统计数据抓取"""
    print("\n🧪 测试4: 单篇文章统计抓取")
    print("-" * 40)
    
    if not articles:
        print("❌ 没有文章可测试")
        return False
    
    try:
        # 测试第一篇文章
        test_article = articles[0]
        print(f"📖 测试文章: {test_article['title'][:40]}...")
        
        stats = spider.extract_article_stats(test_article['url'])
        
        if stats:
            print("✅ 统计数据抓取成功")
            print(f"   标题: {stats['title'][:40]}...")
            print(f"   阅读量: {stats['read_count']:,}")
            print(f"   点赞数: {stats['like_count']:,}")
            print(f"   分享数: {stats['share_count']:,}")
            return True
        else:
            print("❌ 统计数据抓取失败")
            print("💡 可能原因:")
            print("   1. 文章URL无效")
            print("   2. 网络连接问题")
            print("   3. 页面结构变化")
            return False
    except Exception as e:
        print(f"❌ 统计数据抓取出错: {e}")
        return False

def test_data_saving(spider):
    """测试数据保存功能"""
    print("\n🧪 测试5: 数据保存功能")
    print("-" * 40)
    
    # 创建测试数据
    test_data = [{
        'title': '测试文章标题',
        'author': '测试作者',
        'pub_time': '2024-01-01 12:00:00',
        'read_count': 1000,
        'like_count': 50,
        'old_like_count': 45,
        'share_count': 20,
        'comment_count': 10,
        'url': 'https://test.url',
        'digest': '测试摘要',
        'crawl_time': '2024-01-01 12:00:00'
    }]
    
    spider.articles_data = test_data
    
    try:
        # 测试Excel保存
        excel_file = spider.save_to_excel()
        json_file = spider.save_to_json()
        
        success = True
        if excel_file and os.path.exists(excel_file):
            print(f"✅ Excel文件保存成功: {excel_file}")
        else:
            print("❌ Excel文件保存失败")
            success = False
        
        if json_file and os.path.exists(json_file):
            print(f"✅ JSON文件保存成功: {json_file}")
        else:
            print("❌ JSON文件保存失败")
            success = False
        
        return success
    except Exception as e:
        print(f"❌ 数据保存出错: {e}")
        return False

def run_mini_batch_test(spider):
    """运行小规模批量测试"""
    print("\n🧪 测试6: 小规模批量抓取")
    print("-" * 40)
    
    try:
        print("📋 配置: 1页 × 3篇文章，最近30天")
        
        # 小规模测试：1页，3篇文章
        results = spider.batch_crawl_readnum(
            max_pages=1,
            articles_per_page=3,
            days_back=30
        )
        
        if results:
            print(f"✅ 批量抓取成功，获取 {len(results)} 篇文章数据")
            
            # 显示简单统计
            total_reads = sum(r.get('read_count', 0) for r in results)
            total_likes = sum(r.get('like_count', 0) for r in results)
            
            print(f"📊 统计摘要:")
            print(f"   总阅读量: {total_reads:,}")
            print(f"   总点赞数: {total_likes:,}")
            
            # 保存测试数据
            excel_file = spider.save_to_excel()
            if excel_file:
                print(f"💾 测试数据已保存: {excel_file}")
            
            return True
        else:
            print("❌ 批量抓取失败")
            return False
    except Exception as e:
        print(f"❌ 批量抓取出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 批量阅读量抓取器功能测试")
    print("=" * 50)
    
    # 测试结果统计
    test_results = []
    
    # 测试1: Cookie加载
    test_results.append(test_cookie_loading())
    
    if not test_results[0]:
        print("\n❌ Cookie测试失败，无法继续后续测试")
        return
    
    # 测试2: 爬虫初始化
    spider = test_spider_initialization()
    test_results.append(spider is not None)
    
    if not spider:
        print("\n❌ 爬虫初始化失败，无法继续后续测试")
        return
    
    # 测试3: 文章列表获取
    articles = test_article_list_fetching(spider)
    test_results.append(len(articles) > 0)
    
    # 测试4: 单篇文章统计抓取
    if articles:
        test_results.append(test_single_article_stats(spider, articles))
    else:
        test_results.append(False)
    
    # 测试5: 数据保存
    test_results.append(test_data_saving(spider))
    
    # 测试6: 小规模批量抓取（可选）
    print(f"\n是否进行小规模批量抓取测试？(y/n): ", end="")
    if input().lower().strip() == 'y':
        test_results.append(run_mini_batch_test(spider))
    else:
        print("⏭️ 跳过批量抓取测试")
        test_results.append(None)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    test_names = [
        "Cookie加载",
        "爬虫初始化", 
        "文章列表获取",
        "统计数据抓取",
        "数据保存",
        "批量抓取"
    ]
    
    passed = 0
    total = 0
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        if result is None:
            status = "⏭️ 跳过"
        elif result:
            status = "✅ 通过"
            passed += 1
            total += 1
        else:
            status = "❌ 失败"
            total += 1
        
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)" if total > 0 else "\n📊 无有效测试")
    
    if passed == total and total > 0:
        print("🎉 所有测试通过！系统可以正常使用")
    elif passed > 0:
        print("⚠️ 部分测试通过，请检查失败的功能")
    else:
        print("❌ 测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
