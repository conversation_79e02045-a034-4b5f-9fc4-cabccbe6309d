# 微信公众号批量阅读量抓取器

## 功能概述

基于您现有的微信公众号爬虫代码，我为您实现了一个完整的批量阅读量抓取系统，能够：

- 🔍 **批量获取文章链接**：自动获取公众号的文章列表
- 📊 **抓取阅读量统计**：获取每篇文章的阅读量、点赞数、分享数等数据
- 💾 **智能数据保存**：支持Excel和JSON格式导出
- 🛡️ **智能频率控制**：避免被微信服务器封禁
- 🔧 **代理自动管理**：自动处理系统代理设置

## 文件结构

```
wechat_spider/
├── batch_readnum_spider.py    # 批量阅读量抓取器（新增）
├── main_enhanced.py           # 增强版主程序（新增）
├── README_批量阅读量抓取.md    # 使用说明（新增）
├── read_cookie.py             # Cookie读取器（原有）
├── cookie_extractor.py        # Cookie抓取器（原有）
├── wxCrawler.py              # 基础爬虫（原有）
├── utils.py                  # 工具函数（原有）
├── wechat_keys.txt           # Cookie存储文件（原有）
└── data/
    └── readnum_batch/        # 批量阅读量数据存储目录（新增）
```

## 快速开始

### 1. 环境准备

确保已安装必要的Python包：

```bash
pip install requests pandas openpyxl beautifulsoup4 mitmproxy
```

### 2. 运行程序

```bash
python main_enhanced.py
```

### 3. 使用流程

1. **首次使用**：选择功能1抓取Cookie
2. **批量抓取阅读量**：选择功能3进行批量抓取
3. **查看结果**：数据会自动保存到`data/readnum_batch/`目录

## 详细使用说明

### Cookie抓取（功能1）

1. 选择"1. 抓取Cookie"
2. 选择"1. 自动启动抓取器"
3. 程序会自动设置系统代理为127.0.0.1:8080
4. 在浏览器中访问任意微信公众号文章
5. 程序会自动抓取并保存Cookie到`wechat_keys.txt`

### 批量阅读量抓取（功能3）

1. 选择"3. 批量阅读量统计抓取"
2. 配置抓取参数：
   - **最大页数**：要抓取的页数（每页通常10篇文章）
   - **每页文章数**：每页抓取的文章数量
   - **天数范围**：只抓取多少天内的文章
3. 程序会自动：
   - 获取文章列表
   - 逐篇抓取阅读量数据
   - 智能控制请求频率
   - 保存数据到Excel和JSON文件

### 数据输出格式

#### Excel文件包含以下字段：
- 标题
- 作者  
- 发布时间
- 阅读量
- 点赞数
- 历史点赞数
- 分享数
- 评论数
- 文章链接
- 摘要
- 抓取时间

#### JSON文件包含完整的原始数据

## 核心特性

### 1. 智能频率控制
- 请求间隔控制（最小3秒）
- 每10个请求额外延迟
- 自动检测频率限制并调整策略

### 2. 代理自动管理
- 抓取阅读量时自动禁用系统代理
- 完成后自动恢复代理设置
- 避免代理干扰数据抓取

### 3. 错误处理机制
- 网络异常自动重试
- Cookie过期检测
- 数据解析失败处理

### 4. 数据统计分析
- 自动生成统计摘要
- 计算平均阅读量、点赞数等
- 识别阅读量最高的文章

## 使用示例

### 示例1：抓取最近7天的文章
```
最大页数: 3
每页文章数: 10  
天数范围: 7
```
预计抓取：最多30篇文章，最近7天内发布

### 示例2：抓取最近30天的热门文章
```
最大页数: 10
每页文章数: 5
天数范围: 30
```
预计抓取：最多50篇文章，最近30天内发布

## 注意事项

### 1. Cookie有效期
- Cookie通常24小时内有效
- 如果抓取失败，请重新抓取Cookie
- 建议每天重新抓取一次Cookie

### 2. 频率控制
- 请勿设置过高的抓取频率
- 建议单次抓取不超过50篇文章
- 如遇到频率限制，请等待30分钟后重试

### 3. 网络环境
- 确保网络连接稳定
- 抓取过程中请勿关闭程序
- 支持Ctrl+C中断，会保存已抓取的数据

### 4. 数据准确性
- 阅读量数据为抓取时的实时数据
- 不同时间抓取可能有差异
- 建议定期抓取以跟踪数据变化

## 故障排除

### 问题1：Cookie抓取失败
**解决方案**：
1. 检查是否正确访问了微信公众号文章
2. 确认浏览器代理设置为127.0.0.1:8080
3. 尝试访问不同的公众号文章

### 问题2：阅读量抓取失败
**解决方案**：
1. 检查Cookie是否过期（功能4）
2. 重新抓取Cookie（功能1）
3. 降低抓取频率参数

### 问题3：数据保存失败
**解决方案**：
1. 检查磁盘空间是否充足
2. 确认有写入权限
3. 检查文件是否被其他程序占用

## 技术架构

### 核心类：BatchReadnumSpider
- `load_auth_info()`: 加载认证信息
- `get_article_list()`: 获取文章列表
- `extract_article_stats()`: 提取文章统计数据
- `batch_crawl_readnum()`: 批量抓取主函数
- `save_to_excel()`: 保存Excel文件
- `save_to_json()`: 保存JSON文件

### 数据流程
1. 从cookie文件读取认证信息
2. 调用微信API获取文章列表
3. 逐篇访问文章页面提取统计数据
4. 汇总数据并生成报告
5. 保存到Excel和JSON文件

## 更新日志

### v2.0 (当前版本)
- ✅ 新增批量阅读量抓取功能
- ✅ 智能频率控制机制
- ✅ 代理自动管理
- ✅ 数据统计分析
- ✅ 增强版主程序界面

### v1.0 (原版本)
- ✅ 基础文章链接抓取
- ✅ Cookie抓取和管理
- ✅ 简单数据保存

## 联系支持

如果在使用过程中遇到问题，请检查：
1. Python环境和依赖包是否正确安装
2. 网络连接是否稳定
3. Cookie是否有效
4. 抓取参数是否合理

建议在使用前先进行小规模测试（如抓取1页5篇文章）以确保系统正常工作。
